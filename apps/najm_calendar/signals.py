"""
Django signals for najm_calendar app models
"""
import sys
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings

from apps.najm_calendar.models import OccasionDetail

# Safe import with fallback
try:
    from apps.command.tasks.model_translation import translate_occasion_details_batch_task
except ImportError:
    print("⚠️  Translation task not available - translation will be skipped")
    translate_occasion_details_batch_task = None


@receiver(post_save, sender=OccasionDetail)
def auto_translate_occasion_detail(sender, instance, created, **kwargs):
    """
    Signal handler to automatically translate OccasionDetail content when saved.
    
    This uses batch translation with Gemini API to translate content to multiple languages at once.
    """
    
    # Only translate new instances with content
    if not instance.content or not instance.content.strip():
        return

    # Only translate if it's a source language (fa, en, ar)
    if not instance.language or instance.language.code not in ['fa', 'en', 'ar']:
        return

    # Check if translation task is available
    if translate_occasion_details_batch_task is None:
        print(f"⚠️  Translation task not available for OccasionDetail ID {instance.id}")
        return

    try:
        print(f"🚀 Queuing batch translation for OccasionDetail ID {instance.id}")
        print(f"   Occasion: {instance.occasion}")
        print(f"   Language: {instance.language.code if instance.language else 'Unknown'}")
        print(f"   Content length: {len(instance.content)} chars")

        # Queue Celery task for background batch translation
        translate_occasion_details_batch_task.delay(
            model_name='OccasionDetail',
            app_name='najm_calendar',
            occasion_detail_ids=[instance.id],
            batch_size=10,    # Translate to 10 languages at once
            use_gemini=False,    # Use Gemini for batch translation
           use_mock=False      # Set to True for testing
        )

        print(f"✅ Batch translation task queued for OccasionDetail ID {instance.id}")

    except Exception as e:
        print(f"❌ Error queuing batch translation task for OccasionDetail ID {instance.id}: {e}")
        import traceback
        traceback.print_exc()
